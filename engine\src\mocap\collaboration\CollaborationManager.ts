/**
 * 协作管理器
 * 管理多用户协作交互和冲突检测
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import type { Entity } from '../../core/Entity';
import { Vector3 } from 'three';

/**
 * 协作类型
 */
export enum CollaborationType {
  HANDOFF = 'handoff',
  COLLABORATIVE_MOVE = 'collaborative_move',
  SYNCHRONIZED_ACTION = 'synchronized_action',
  SHARED_INTERACTION = 'shared_interaction',
  COMPETITIVE_INTERACTION = 'competitive_interaction'
}

/**
 * 协作事件
 */
export interface CollaborationEvent {
  type: CollaborationType;
  participants: string[];
  targetObject?: Entity;
  startTime: number;
  confidence: number;
  data: any;
}

/**
 * 协作区域
 */
export interface CollaborationZone {
  id: string;
  center: Vector3;
  radius: number;
  participants: Set<string>;
  activeInteractions: Map<string, any>;
  priority: number;
}

/**
 * 协作管理器
 */
export class CollaborationManager extends EventEmitter {
  private config: any;
  private collaborationZones: Map<string, CollaborationZone> = new Map();
  private activeCollaborations: Map<string, CollaborationEvent> = new Map();
  private userProximity: Map<string, Map<string, number>> = new Map();
  private interactionHistory: CollaborationEvent[] = [];

  constructor(config: any) {
    super();
    this.config = config;
  }

  /**
   * 检测协作交互
   */
  public detectCollaboration(
    localEntity: Entity,
    localData: any,
    remoteUsers: any[]
  ): void {
    // 更新用户邻近度
    this.updateUserProximity(localEntity, remoteUsers);

    // 检测不同类型的协作
    this.detectHandoff(localEntity, localData, remoteUsers);
    this.detectCollaborativeMove(localEntity, localData, remoteUsers);
    this.detectSynchronizedAction(localEntity, localData, remoteUsers);
    this.detectSharedInteraction(localEntity, localData, remoteUsers);
  }

  /**
   * 更新用户邻近度
   */
  private updateUserProximity(localEntity: Entity, remoteUsers: any[]): void {
    const localTransform = localEntity.getComponent('Transform');
    if (!localTransform) return;

    const localUserId = localEntity.id;
    if (!this.userProximity.has(localUserId)) {
      this.userProximity.set(localUserId, new Map());
    }

    const proximityMap = this.userProximity.get(localUserId)!;

    for (const remoteUser of remoteUsers) {
      if (!remoteUser.entity) continue;

      const remoteTransform = remoteUser.entity.getComponent('Transform');
      if (!remoteTransform) continue;

      // 获取位置属性
      const localPos = (localTransform as any).position || localTransform.getPosition?.() || new Vector3();
      const remotePos = (remoteTransform as any).position || remoteTransform.getPosition?.() || new Vector3();

      const distance = localPos.distanceTo(remotePos);
      proximityMap.set(remoteUser.id, distance);

      // 检查是否进入协作区域
      this.checkCollaborationZone(localUserId, remoteUser.id, distance);
    }
  }

  /**
   * 检查协作区域
   */
  private checkCollaborationZone(userId1: string, userId2: string, distance: number): void {
    const collaborationThreshold = 2.0; // 2米协作距离

    if (distance <= collaborationThreshold) {
      // 创建或更新协作区域
      const zoneId = this.getZoneId(userId1, userId2);
      let zone = this.collaborationZones.get(zoneId);

      if (!zone) {
        zone = {
          id: zoneId,
          center: new Vector3(), // 需要计算中心点
          radius: collaborationThreshold,
          participants: new Set([userId1, userId2]),
          activeInteractions: new Map(),
          priority: 1
        };
        this.collaborationZones.set(zoneId, zone);
        this.emit('collaborationZoneCreated', zone);
      } else {
        zone.participants.add(userId1);
        zone.participants.add(userId2);
      }
    }
  }

  /**
   * 检测交接协作
   */
  private detectHandoff(localEntity: Entity, localData: any, remoteUsers: any[]): void {
    const grabberComponent = localEntity.getComponent('GrabberComponent');
    if (!grabberComponent) return;

    // 检查是否正在释放物体
    if (localData.leftAdvancedGesture?.type === 'release' ||
        localData.rightAdvancedGesture?.type === 'release') {

      const releasedObject = (grabberComponent as any).getGrabbedObject?.();
      if (!releasedObject) return;

      // 检查附近是否有用户准备抓取
      for (const remoteUser of remoteUsers) {
        if (this.isUserReadyToGrab(remoteUser, releasedObject)) {
          const handoffEvent: CollaborationEvent = {
            type: CollaborationType.HANDOFF,
            participants: [localEntity.id, remoteUser.id],
            targetObject: releasedObject,
            startTime: Date.now(),
            confidence: 0.8,
            data: {
              releaser: localEntity.id,
              receiver: remoteUser.id,
              object: releasedObject.id
            }
          };

          this.registerCollaboration(handoffEvent);
        }
      }
    }
  }

  /**
   * 检测协作移动
   */
  private detectCollaborativeMove(localEntity: Entity, _localData: any, remoteUsers: any[]): void {
    const grabberComponent = localEntity.getComponent('GrabberComponent');
    if (!grabberComponent) return;

    const grabbedObject = (grabberComponent as any).getGrabbedObject?.();
    if (!grabbedObject) return;

    // 检查是否有其他用户也在抓取同一物体
    for (const remoteUser of remoteUsers) {
      if (this.isUserGrabbingSameObject(remoteUser, grabbedObject)) {
        const collaborativeEvent: CollaborationEvent = {
          type: CollaborationType.COLLABORATIVE_MOVE,
          participants: [localEntity.id, remoteUser.id],
          targetObject: grabbedObject,
          startTime: Date.now(),
          confidence: 0.9,
          data: {
            object: grabbedObject.id,
            participants: [localEntity.id, remoteUser.id]
          }
        };

        this.registerCollaboration(collaborativeEvent);
      }
    }
  }

  /**
   * 检测同步动作
   */
  private detectSynchronizedAction(localEntity: Entity, localData: any, remoteUsers: any[]): void {
    const localGesture = localData.leftAdvancedGesture || localData.rightAdvancedGesture;
    if (!localGesture) return;

    for (const remoteUser of remoteUsers) {
      const proximity = this.getUserProximity(localEntity.id, remoteUser.id);
      if (proximity && proximity <= 3.0) { // 3米内

        // 检查是否执行相似手势
        if (this.isSimilarGesture(localGesture, remoteUser.lastGesture)) {
          const syncEvent: CollaborationEvent = {
            type: CollaborationType.SYNCHRONIZED_ACTION,
            participants: [localEntity.id, remoteUser.id],
            startTime: Date.now(),
            confidence: 0.7,
            data: {
              gesture: localGesture.type,
              participants: [localEntity.id, remoteUser.id],
              proximity
            }
          };

          this.registerCollaboration(syncEvent);
        }
      }
    }
  }

  /**
   * 检测共享交互
   */
  private detectSharedInteraction(_localEntity: Entity, _localData: any, _remoteUsers: any[]): void {
    // 检查是否多个用户同时与同一区域交互
    const interactionZones = this.getActiveInteractionZones();

    for (const zone of interactionZones) {
      if (zone.participants.size >= 2) {
        const sharedEvent: CollaborationEvent = {
          type: CollaborationType.SHARED_INTERACTION,
          participants: Array.from(zone.participants),
          startTime: Date.now(),
          confidence: 0.6,
          data: {
            zone: zone.id,
            participants: Array.from(zone.participants)
          }
        };

        this.registerCollaboration(sharedEvent);
      }
    }
  }

  /**
   * 注册协作事件
   */
  private registerCollaboration(event: CollaborationEvent): void {
    const eventId = this.generateEventId(event);
    
    // 避免重复事件
    if (this.activeCollaborations.has(eventId)) {
      return;
    }

    this.activeCollaborations.set(eventId, event);
    this.interactionHistory.push(event);

    // 限制历史记录大小
    if (this.interactionHistory.length > 100) {
      this.interactionHistory.shift();
    }

    Debug.log('CollaborationManager', '检测到协作事件', event);
    this.emit('collaborationDetected', event);

    // 设置事件过期
    setTimeout(() => {
      this.activeCollaborations.delete(eventId);
    }, 5000); // 5秒后过期
  }

  /**
   * 检查用户是否准备抓取
   */
  private isUserReadyToGrab(user: any, targetObject: Entity): boolean {
    if (!user.entity) return false;

    const grabberComponent = user.entity.getComponent('GrabberComponent');
    if (!grabberComponent) return false;

    // 检查手势
    const gesture = user.lastGesture;
    if (!gesture || gesture.type !== 'grab') return false;

    // 检查距离
    const userTransform = user.entity.getComponent('Transform');
    const objectTransform = targetObject.getComponent('Transform');

    if (!userTransform || !objectTransform) return false;

    // 获取位置属性
    const userPos = (userTransform as any).position || (userTransform as any).getPosition?.() || new Vector3();
    const objectPos = (objectTransform as any).position || (objectTransform as any).getPosition?.() || new Vector3();

    const distance = userPos.distanceTo(objectPos);
    return distance <= 1.5; // 1.5米内
  }

  /**
   * 检查用户是否抓取同一物体
   */
  private isUserGrabbingSameObject(user: any, targetObject: Entity): boolean {
    if (!user.entity) return false;

    const grabberComponent = user.entity.getComponent('GrabberComponent');
    if (!grabberComponent) return false;

    const userGrabbedObject = (grabberComponent as any).getGrabbedObject?.();
    return userGrabbedObject === targetObject;
  }

  /**
   * 检查手势相似性
   */
  private isSimilarGesture(gesture1: any, gesture2: any): boolean {
    if (!gesture1 || !gesture2) return false;
    
    // 检查手势类型
    if (gesture1.type === gesture2.type) return true;
    
    // 检查手势族群
    const gestureGroups = {
      pointing: ['pointing', 'finger_gun'],
      grabbing: ['grab', 'closed_fist'],
      opening: ['open_hand', 'release', 'spread_fingers']
    };

    for (const group of Object.values(gestureGroups)) {
      if (group.includes(gesture1.type) && group.includes(gesture2.type)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 获取用户邻近度
   */
  private getUserProximity(userId1: string, userId2: string): number | null {
    const proximityMap = this.userProximity.get(userId1);
    return proximityMap ? proximityMap.get(userId2) || null : null;
  }

  /**
   * 获取活跃交互区域
   */
  private getActiveInteractionZones(): CollaborationZone[] {
    return Array.from(this.collaborationZones.values())
      .filter(zone => zone.participants.size > 0);
  }

  /**
   * 生成区域ID
   */
  private getZoneId(userId1: string, userId2: string): string {
    const sortedIds = [userId1, userId2].sort();
    return `zone_${sortedIds.join('_')}`;
  }

  /**
   * 生成事件ID
   */
  private generateEventId(event: CollaborationEvent): string {
    const participantIds = event.participants.sort().join('_');
    return `${event.type}_${participantIds}_${event.startTime}`;
  }

  /**
   * 获取活跃协作
   */
  public getActiveCollaborations(): CollaborationEvent[] {
    return Array.from(this.activeCollaborations.values());
  }

  /**
   * 获取协作历史
   */
  public getCollaborationHistory(): CollaborationEvent[] {
    return [...this.interactionHistory];
  }

  /**
   * 获取协作区域
   */
  public getCollaborationZones(): CollaborationZone[] {
    return Array.from(this.collaborationZones.values());
  }

  /**
   * 清理过期区域
   */
  public cleanupExpiredZones(): void {
    for (const [zoneId, zone] of this.collaborationZones.entries()) {
      // 检查区域是否有最近活动
      let hasRecentActivity = false;

      for (const _participantId of zone.participants) {
        // 这里应该检查用户最后活动时间
        // 简化实现，假设总是有活动
        hasRecentActivity = true;
        break;
      }

      if (!hasRecentActivity) {
        this.collaborationZones.delete(zoneId);
        this.emit('collaborationZoneExpired', zone);
      }
    }
  }

  /**
   * 获取统计信息
   */
  public getStats(): any {
    return {
      activeCollaborations: this.activeCollaborations.size,
      collaborationZones: this.collaborationZones.size,
      totalCollaborations: this.interactionHistory.length,
      collaborationTypes: this.getCollaborationTypeStats()
    };
  }

  /**
   * 获取协作类型统计
   */
  private getCollaborationTypeStats(): any {
    const stats: any = {};
    
    for (const event of this.interactionHistory) {
      stats[event.type] = (stats[event.type] || 0) + 1;
    }
    
    return stats;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.collaborationZones.clear();
    this.activeCollaborations.clear();
    this.userProximity.clear();
    this.interactionHistory = [];
    this.removeAllListeners();
  }
}
