/**
 * 协作组件
 * 用于存储用户协作相关的数据
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';

/**
 * 用户角色
 */
export enum UserRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  PARTICIPANT = 'participant',
  OBSERVER = 'observer'
}

/**
 * 用户权限
 */
export interface UserPermissions {
  canInteract: boolean;
  canModifyObjects: boolean;
  canInviteUsers: boolean;
  canManageSession: boolean;
  maxInteractionDistance: number;
}

/**
 * 协作组件配置
 */
export interface CollaborationComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 用户ID */
  userId?: string;
  /** 用户角色 */
  role?: UserRole;
  /** 用户权限 */
  permissions?: UserPermissions;
}

/**
 * 协作组件
 * 存储用户协作相关的数据和状态
 */
export class CollaborationComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'CollaborationComponent';

  /** 用户ID */
  public userId: string;
  
  /** 用户角色 */
  public role: UserRole;
  
  /** 用户权限 */
  public permissions: UserPermissions;
  
  /** 是否是远程用户 */
  public isRemote: boolean;
  
  /** 最后活动时间 */
  public lastActivity: number;

  /**
   * 构造函数
   * @param entity 实体
   * @param config 组件配置
   */
  constructor(entity: Entity, config: CollaborationComponentConfig = {}) {
    super(CollaborationComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    // 设置启用状态
    this.setEnabled(config.enabled !== undefined ? config.enabled : true);
    
    this.userId = config.userId || '';
    this.role = config.role || UserRole.PARTICIPANT;
    this.permissions = config.permissions || {
      canInteract: true,
      canModifyObjects: false,
      canInviteUsers: false,
      canManageSession: false,
      maxInteractionDistance: 5.0
    };
    this.isRemote = false;
    this.lastActivity = Date.now();
  }

  /**
   * 更新最后活动时间
   */
  public updateActivity(): void {
    this.lastActivity = Date.now();
  }

  /**
   * 检查权限
   */
  public hasPermission(permission: keyof UserPermissions): boolean {
    return this.permissions[permission] as boolean;
  }

  /**
   * 设置权限
   */
  public setPermission(permission: keyof UserPermissions, value: any): void {
    (this.permissions as any)[permission] = value;
  }

  /**
   * 设置角色
   */
  public setRole(role: UserRole): void {
    this.role = role;
    
    // 根据角色设置默认权限
    switch (role) {
      case UserRole.ADMIN:
        this.permissions = {
          canInteract: true,
          canModifyObjects: true,
          canInviteUsers: true,
          canManageSession: true,
          maxInteractionDistance: 10.0
        };
        break;
      case UserRole.MODERATOR:
        this.permissions = {
          canInteract: true,
          canModifyObjects: true,
          canInviteUsers: true,
          canManageSession: false,
          maxInteractionDistance: 8.0
        };
        break;
      case UserRole.PARTICIPANT:
        this.permissions = {
          canInteract: true,
          canModifyObjects: true,
          canInviteUsers: false,
          canManageSession: false,
          maxInteractionDistance: 5.0
        };
        break;
      case UserRole.OBSERVER:
        this.permissions = {
          canInteract: false,
          canModifyObjects: false,
          canInviteUsers: false,
          canManageSession: false,
          maxInteractionDistance: 0.0
        };
        break;
    }
  }

  /**
   * 获取活动状态
   */
  public isActive(): boolean {
    const inactiveThreshold = 30000; // 30秒
    return Date.now() - this.lastActivity < inactiveThreshold;
  }

  /**
   * 序列化组件数据
   */
  public serialize(): any {
    return {
      type: this.getType(),
      enabled: this.isEnabled(),
      userId: this.userId,
      role: this.role,
      permissions: this.permissions,
      isRemote: this.isRemote,
      lastActivity: this.lastActivity
    };
  }

  /**
   * 反序列化组件数据
   */
  public deserialize(data: any): void {
    this.setEnabled(data.enabled);
    this.userId = data.userId || '';
    this.role = data.role || UserRole.PARTICIPANT;
    this.permissions = data.permissions || this.permissions;
    this.isRemote = data.isRemote || false;
    this.lastActivity = data.lastActivity || Date.now();
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new CollaborationComponent(this.getEntity()!, {
      enabled: this.isEnabled(),
      userId: this.userId,
      role: this.role,
      permissions: this.permissions
    });
  }

  /**
   * 克隆组件
   * @param entity 目标实体
   * @returns 克隆的组件
   */
  public clone(entity?: Entity): CollaborationComponent {
    const targetEntity = entity || this.getEntity()!;
    const component = new CollaborationComponent(targetEntity, {
      enabled: this.isEnabled(),
      userId: this.userId,
      role: this.role,
      permissions: { ...this.permissions }
    });
    
    component.isRemote = this.isRemote;
    component.lastActivity = this.lastActivity;
    
    return component;
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    super.destroy();
  }
}
