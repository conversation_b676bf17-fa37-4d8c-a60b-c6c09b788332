/**
 * 高级手势识别器
 * 提供更精确的手势识别、动态手势识别和手势序列识别功能
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { LandmarkData } from '../types/LandmarkData';
import { GestureType, GestureResult } from '../interaction/ActionMappingSystem';
import { Vector3 } from 'three';

/**
 * 扩展的手势类型
 */
export enum AdvancedGestureType {
  // 基础手势
  OPEN_HAND = 'open_hand',
  CLOSED_FIST = 'closed_fist',
  POINTING = 'pointing',
  PEACE_SIGN = 'peace_sign',
  THUMBS_UP = 'thumbs_up',
  THUMBS_DOWN = 'thumbs_down',
  GRAB = 'grab',
  RELEASE = 'release',

  // 高级手势
  OK_SIGN = 'ok_sign',
  ROCK_SIGN = 'rock_sign',
  CALL_ME = 'call_me',
  FINGER_GUN = 'finger_gun',
  PINCH = 'pinch',
  SPREAD_FINGERS = 'spread_fingers',

  // 动态手势
  WAVE = 'wave',
  SWIPE_LEFT = 'swipe_left',
  SWIPE_RIGHT = 'swipe_right',
  SWIPE_UP = 'swipe_up',
  SWIPE_DOWN = 'swipe_down',
  CIRCLE_CLOCKWISE = 'circle_clockwise',
  CIRCLE_COUNTER_CLOCKWISE = 'circle_counter_clockwise',

  // 精细手势
  FINGER_COUNT_1 = 'finger_count_1',
  FINGER_COUNT_2 = 'finger_count_2',
  FINGER_COUNT_3 = 'finger_count_3',
  FINGER_COUNT_4 = 'finger_count_4',
  FINGER_COUNT_5 = 'finger_count_5',

  // 手势序列
  DOUBLE_TAP = 'double_tap',
  TRIPLE_TAP = 'triple_tap',
  LONG_PRESS = 'long_press'
}

/**
 * 高级手势识别结果
 */
export interface AdvancedGestureResult extends GestureResult {
  type: AdvancedGestureType;
  /** 手势持续时间 */
  duration: number;
  /** 手势速度 */
  velocity: Vector3;
  /** 手势轨迹 */
  trajectory?: Vector3[];
  /** 手势特征 */
  features: GestureFeatures;
}

/**
 * 手势特征
 */
export interface GestureFeatures {
  /** 手指弯曲度 */
  fingerCurvatures: {
    thumb: number;
    index: number;
    middle: number;
    ring: number;
    pinky: number;
  };
  /** 手指间距 */
  fingerDistances: number[];
  /** 手掌方向 */
  palmDirection: Vector3;
  /** 手掌法向量 */
  palmNormal: Vector3;
  /** 手部开合度 */
  handOpenness: number;
  /** 手部倾斜角度 */
  handTilt: number;
}

/**
 * 动态手势历史数据
 */
interface DynamicGestureHistory {
  positions: Vector3[];
  timestamps: number[];
  gestures: AdvancedGestureType[];
  maxHistorySize: number;
}

/**
 * 手势序列模式
 */
interface GestureSequencePattern {
  name: string;
  pattern: AdvancedGestureType[];
  maxInterval: number; // 最大间隔时间（毫秒）
  minConfidence: number;
}

/**
 * 高级手势识别配置
 */
export interface AdvancedGestureConfig {
  /** 是否启用动态手势识别 */
  enableDynamicGestures: boolean;
  /** 是否启用手势序列识别 */
  enableSequenceRecognition: boolean;
  /** 是否启用精细手势识别 */
  enableFineGestures: boolean;
  /** 动态手势历史大小 */
  dynamicHistorySize: number;
  /** 最小手势持续时间 */
  minGestureDuration: number;
  /** 最大手势持续时间 */
  maxGestureDuration: number;
  /** 动态手势速度阈值 */
  velocityThreshold: number;
  /** 手势稳定性阈值 */
  stabilityThreshold: number;
  /** 置信度阈值 */
  confidenceThreshold: number;
}

/**
 * 高级手势识别器
 */
export class AdvancedGestureRecognizer extends EventEmitter {
  private config: AdvancedGestureConfig;
  private gestureHistory: Map<string, DynamicGestureHistory> = new Map();
  private currentGestures: Map<string, AdvancedGestureResult> = new Map();
  private gestureStartTimes: Map<string, number> = new Map();
  private sequencePatterns: GestureSequencePattern[] = [];
  private lastProcessTime = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AdvancedGestureConfig = {
    enableDynamicGestures: true,
    enableSequenceRecognition: true,
    enableFineGestures: true,
    dynamicHistorySize: 30,
    minGestureDuration: 100,
    maxGestureDuration: 5000,
    velocityThreshold: 0.01,
    stabilityThreshold: 0.005,
    confidenceThreshold: 0.7
  };

  constructor(config: Partial<AdvancedGestureConfig> = {}) {
    super();
    this.config = { ...AdvancedGestureRecognizer.DEFAULT_CONFIG, ...config };
    this.initializeSequencePatterns();
  }

  /**
   * 初始化手势序列模式
   */
  private initializeSequencePatterns(): void {
    this.sequencePatterns = [
      {
        name: 'double_tap',
        pattern: [AdvancedGestureType.POINTING, AdvancedGestureType.CLOSED_FIST, AdvancedGestureType.POINTING],
        maxInterval: 500,
        minConfidence: 0.8
      },
      {
        name: 'wave_hello',
        pattern: [AdvancedGestureType.OPEN_HAND, AdvancedGestureType.WAVE, AdvancedGestureType.OPEN_HAND],
        maxInterval: 1000,
        minConfidence: 0.7
      },
      {
        name: 'count_to_three',
        pattern: [AdvancedGestureType.FINGER_COUNT_1, AdvancedGestureType.FINGER_COUNT_2, AdvancedGestureType.FINGER_COUNT_3],
        maxInterval: 2000,
        minConfidence: 0.8
      }
    ];
  }

  /**
   * 识别手势
   */
  public recognizeGesture(handLandmarks: LandmarkData[], handType: 'left' | 'right'): AdvancedGestureResult | null {
    if (handLandmarks.length < 21) {
      return null;
    }

    try {
      const now = Date.now();
      const handKey = handType;

      // 计算手势特征
      const features = this.extractGestureFeatures(handLandmarks);

      // 计算手部位置
      const handPosition = this.calculateHandPosition(handLandmarks);

      // 更新动态历史
      this.updateDynamicHistory(handKey, handPosition, now);

      // 识别静态手势
      const staticGesture = this.recognizeStaticGesture(features, handType, handPosition);

      // 识别动态手势
      let dynamicGesture: AdvancedGestureResult | null = null;
      if (this.config.enableDynamicGestures) {
        dynamicGesture = this.recognizeDynamicGesture(handKey, features, handPosition);
      }

      // 选择最佳手势
      const bestGesture = this.selectBestGesture(staticGesture, dynamicGesture);

      if (bestGesture) {
        // 更新手势持续时间
        this.updateGestureDuration(handKey, bestGesture, now);

        // 检查手势序列
        if (this.config.enableSequenceRecognition) {
          this.checkGestureSequences(handKey, bestGesture);
        }

        // 更新当前手势
        this.currentGestures.set(handKey, bestGesture);

        return bestGesture;
      }

    } catch (error) {
      Debug.error('AdvancedGestureRecognizer', '手势识别失败', error);
    }

    return null;
  }

  /**
   * 提取手势特征
   */
  private extractGestureFeatures(handLandmarks: LandmarkData[]): GestureFeatures {
    // 计算手指弯曲度
    const fingerCurvatures = this.calculateFingerCurvatures(handLandmarks);

    // 计算手指间距
    const fingerDistances = this.calculateFingerDistances(handLandmarks);

    // 计算手掌方向和法向量
    const palmDirection = this.calculatePalmDirection(handLandmarks);
    const palmNormal = this.calculatePalmNormal(handLandmarks);

    // 计算手部开合度
    const handOpenness = this.calculateHandOpenness(handLandmarks);

    // 计算手部倾斜角度
    const handTilt = this.calculateHandTilt(handLandmarks);

    return {
      fingerCurvatures,
      fingerDistances,
      palmDirection,
      palmNormal,
      handOpenness,
      handTilt
    };
  }

  /**
   * 计算手指弯曲度（增强版）
   */
  private calculateFingerCurvatures(handLandmarks: LandmarkData[]): any {
    const fingerTips = [4, 8, 12, 16, 20];
    const fingerPips = [3, 6, 10, 14, 18];
    const fingerMcps = [2, 5, 9, 13, 17];
    const fingerDips = [0, 7, 11, 15, 19]; // DIP关节（拇指没有DIP）

    const curvatures = {
      thumb: 0,
      index: 0,
      middle: 0,
      ring: 0,
      pinky: 0
    };

    const fingerNames = ['thumb', 'index', 'middle', 'ring', 'pinky'] as const;

    for (let i = 0; i < 5; i++) {
      const tip = handLandmarks[fingerTips[i]];
      const pip = handLandmarks[fingerPips[i]];
      const mcp = handLandmarks[fingerMcps[i]];

      if (tip && pip && mcp) {
        // 使用更精确的角度计算
        const vec1 = new Vector3(pip.x - mcp.x, pip.y - mcp.y, (pip.z || 0) - (mcp.z || 0));
        const vec2 = new Vector3(tip.x - pip.x, tip.y - pip.y, (tip.z || 0) - (pip.z || 0));

        const angle = vec1.angleTo(vec2);
        curvatures[fingerNames[i]] = Math.min(1, angle / Math.PI);
      }
    }

    return curvatures;
  }

  /**
   * 计算手指间距
   */
  private calculateFingerDistances(handLandmarks: LandmarkData[]): number[] {
    const fingerTips = [4, 8, 12, 16, 20];
    const distances: number[] = [];

    for (let i = 0; i < fingerTips.length - 1; i++) {
      const tip1 = handLandmarks[fingerTips[i]];
      const tip2 = handLandmarks[fingerTips[i + 1]];

      if (tip1 && tip2) {
        const distance = Math.sqrt(
          Math.pow(tip2.x - tip1.x, 2) +
          Math.pow(tip2.y - tip1.y, 2) +
          Math.pow((tip2.z || 0) - (tip1.z || 0), 2)
        );
        distances.push(distance);
      } else {
        distances.push(0);
      }
    }

    return distances;
  }

  /**
   * 计算手掌方向
   */
  private calculatePalmDirection(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];

    if (wrist && middleMcp) {
      return new Vector3(
        middleMcp.x - wrist.x,
        middleMcp.y - wrist.y,
        (middleMcp.z || 0) - (wrist.z || 0)
      ).normalize();
    }

    return new Vector3(0, 1, 0);
  }

  /**
   * 计算手掌法向量
   */
  private calculatePalmNormal(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0];
    const indexMcp = handLandmarks[5];
    const pinkyMcp = handLandmarks[17];

    if (wrist && indexMcp && pinkyMcp) {
      const vec1 = new Vector3(
        indexMcp.x - wrist.x,
        indexMcp.y - wrist.y,
        (indexMcp.z || 0) - (wrist.z || 0)
      );

      const vec2 = new Vector3(
        pinkyMcp.x - wrist.x,
        pinkyMcp.y - wrist.y,
        (pinkyMcp.z || 0) - (wrist.z || 0)
      );

      return new Vector3().crossVectors(vec1, vec2).normalize();
    }

    return new Vector3(0, 0, 1);
  }

  /**
   * 计算手部开合度
   */
  private calculateHandOpenness(handLandmarks: LandmarkData[]): number {
    const fingerTips = [4, 8, 12, 16, 20];
    const wrist = handLandmarks[0];

    if (!wrist) return 0;

    let totalDistance = 0;
    let validFingers = 0;

    for (const tipIndex of fingerTips) {
      const tip = handLandmarks[tipIndex];
      if (tip) {
        const distance = Math.sqrt(
          Math.pow(tip.x - wrist.x, 2) +
          Math.pow(tip.y - wrist.y, 2)
        );
        totalDistance += distance;
        validFingers++;
      }
    }

    return validFingers > 0 ? totalDistance / validFingers : 0;
  }

  /**
   * 计算手部倾斜角度
   */
  private calculateHandTilt(handLandmarks: LandmarkData[]): number {
    const wrist = handLandmarks[0];
    const middleMcp = handLandmarks[9];

    if (wrist && middleMcp) {
      const angle = Math.atan2(middleMcp.y - wrist.y, middleMcp.x - wrist.x);
      return angle;
    }

    return 0;
  }

  /**
   * 计算手部位置
   */
  private calculateHandPosition(handLandmarks: LandmarkData[]): Vector3 {
    const wrist = handLandmarks[0];

    if (wrist) {
      return new Vector3(
        (wrist.x - 0.5) * 2,
        -(wrist.y - 0.5) * 2,
        wrist.z || 0
      );
    }

    return new Vector3();
  }

  /**
   * 识别静态手势
   */
  private recognizeStaticGesture(features: GestureFeatures, handType: 'left' | 'right', position: Vector3): AdvancedGestureResult | null {
    const { fingerCurvatures, fingerDistances, handOpenness } = features;

    // OK手势识别
    if (this.isOKGesture(fingerCurvatures, fingerDistances)) {
      return this.createGestureResult(AdvancedGestureType.OK_SIGN, handType, position, features, 0.9);
    }

    // 摇滚手势识别
    if (this.isRockGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.ROCK_SIGN, handType, position, features, 0.85);
    }

    // 打电话手势识别
    if (this.isCallMeGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.CALL_ME, handType, position, features, 0.8);
    }

    // 手枪手势识别
    if (this.isFingerGunGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.FINGER_GUN, handType, position, features, 0.8);
    }

    // 捏取手势识别
    if (this.isPinchGesture(fingerCurvatures, fingerDistances)) {
      return this.createGestureResult(AdvancedGestureType.PINCH, handType, position, features, 0.85);
    }

    // 张开手指手势识别
    if (this.isSpreadFingersGesture(fingerCurvatures, fingerDistances)) {
      return this.createGestureResult(AdvancedGestureType.SPREAD_FINGERS, handType, position, features, 0.8);
    }

    // 数字手势识别
    const fingerCount = this.countExtendedFingers(fingerCurvatures);
    if (fingerCount >= 1 && fingerCount <= 5) {
      const gestureType = this.getFingerCountGesture(fingerCount);
      return this.createGestureResult(gestureType, handType, position, features, 0.8);
    }

    // 基础手势识别（从原有系统继承）
    return this.recognizeBasicGesture(fingerCurvatures, handType, position, features);
  }

  /**
   * 识别基础手势
   */
  private recognizeBasicGesture(fingerCurvatures: any, handType: 'left' | 'right', position: Vector3, features: GestureFeatures): AdvancedGestureResult | null {
    // 抓取手势
    if (this.isGrabGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.GRAB, handType, position, features, this.calculateGrabConfidence(fingerCurvatures));
    }

    // 张开手势
    if (this.isOpenHandGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.OPEN_HAND, handType, position, features, this.calculateOpenHandConfidence(fingerCurvatures));
    }

    // 指向手势
    if (this.isPointingGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.POINTING, handType, position, features, this.calculatePointingConfidence(fingerCurvatures));
    }

    // 竖拇指手势
    if (this.isThumbsUpGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.THUMBS_UP, handType, position, features, this.calculateThumbsUpConfidence(fingerCurvatures));
    }

    // 拇指向下手势
    if (this.isThumbsDownGesture(fingerCurvatures)) {
      return this.createGestureResult(AdvancedGestureType.THUMBS_DOWN, handType, position, features, 0.8);
    }

    return null;
  }

  /**
   * 识别动态手势
   */
  private recognizeDynamicGesture(handKey: string, features: GestureFeatures, position: Vector3): AdvancedGestureResult | null {
    const history = this.gestureHistory.get(handKey);
    if (!history || history.positions.length < 5) {
      return null;
    }

    // 计算运动轨迹
    const trajectory = this.analyzeTrajectory(history.positions);
    const velocity = this.calculateVelocity(history.positions, history.timestamps);

    // 挥手手势识别
    if (this.isWaveGesture(trajectory, velocity)) {
      return this.createDynamicGestureResult(AdvancedGestureType.WAVE, handKey, position, features, trajectory, velocity, 0.8);
    }

    // 滑动手势识别
    const swipeDirection = this.detectSwipeDirection(trajectory, velocity);
    if (swipeDirection) {
      return this.createDynamicGestureResult(swipeDirection, handKey, position, features, trajectory, velocity, 0.85);
    }

    // 圆形手势识别
    const circleDirection = this.detectCircleGesture(trajectory);
    if (circleDirection) {
      return this.createDynamicGestureResult(circleDirection, handKey, position, features, trajectory, velocity, 0.8);
    }

    return null;
  }

  /**
   * 更新动态历史
   */
  private updateDynamicHistory(handKey: string, position: Vector3, timestamp: number): void {
    if (!this.gestureHistory.has(handKey)) {
      this.gestureHistory.set(handKey, {
        positions: [],
        timestamps: [],
        gestures: [],
        maxHistorySize: this.config.dynamicHistorySize
      });
    }

    const history = this.gestureHistory.get(handKey)!;
    history.positions.push(position.clone());
    history.timestamps.push(timestamp);

    // 限制历史大小
    if (history.positions.length > history.maxHistorySize) {
      history.positions.shift();
      history.timestamps.shift();
      if (history.gestures.length > 0) {
        history.gestures.shift();
      }
    }
  }

  /**
   * 分析轨迹
   */
  private analyzeTrajectory(positions: Vector3[]): Vector3[] {
    if (positions.length < 2) return [];

    const trajectory: Vector3[] = [];
    for (let i = 1; i < positions.length; i++) {
      const direction = positions[i].clone().sub(positions[i - 1]);
      trajectory.push(direction);
    }

    return trajectory;
  }

  /**
   * 计算速度
   */
  private calculateVelocity(positions: Vector3[], timestamps: number[]): Vector3 {
    if (positions.length < 2) return new Vector3();

    const recentPositions = positions.slice(-5);
    const recentTimestamps = timestamps.slice(-5);

    if (recentPositions.length < 2) return new Vector3();

    const totalDisplacement = recentPositions[recentPositions.length - 1].clone().sub(recentPositions[0]);
    const totalTime = recentTimestamps[recentTimestamps.length - 1] - recentTimestamps[0];

    return totalTime > 0 ? totalDisplacement.divideScalar(totalTime / 1000) : new Vector3();
  }

  /**
   * 检测挥手手势
   */
  private isWaveGesture(trajectory: Vector3[], velocity: Vector3): boolean {
    if (trajectory.length < 10) return false;

    // 检测左右摆动模式
    let directionChanges = 0;
    let lastDirection = 0;

    for (const movement of trajectory) {
      const currentDirection = Math.sign(movement.x);
      if (currentDirection !== 0 && currentDirection !== lastDirection && lastDirection !== 0) {
        directionChanges++;
      }
      if (currentDirection !== 0) {
        lastDirection = currentDirection;
      }
    }

    return directionChanges >= 3 && velocity.length() > this.config.velocityThreshold;
  }

  /**
   * 检测滑动方向
   */
  private detectSwipeDirection(trajectory: Vector3[], velocity: Vector3): AdvancedGestureType | null {
    if (velocity.length() < this.config.velocityThreshold) return null;

    const totalMovement = trajectory.reduce((sum, movement) => sum.add(movement), new Vector3());
    const dominantAxis = this.getDominantAxis(totalMovement);

    if (dominantAxis === 'x') {
      return totalMovement.x > 0 ? AdvancedGestureType.SWIPE_RIGHT : AdvancedGestureType.SWIPE_LEFT;
    } else if (dominantAxis === 'y') {
      return totalMovement.y > 0 ? AdvancedGestureType.SWIPE_UP : AdvancedGestureType.SWIPE_DOWN;
    }

    return null;
  }

  /**
   * 检测圆形手势
   */
  private detectCircleGesture(trajectory: Vector3[]): AdvancedGestureType | null {
    if (trajectory.length < 15) return null;

    // 计算角度变化
    let totalAngleChange = 0;
    for (let i = 1; i < trajectory.length; i++) {
      const angle1 = Math.atan2(trajectory[i - 1].y, trajectory[i - 1].x);
      const angle2 = Math.atan2(trajectory[i].y, trajectory[i].x);
      let angleDiff = angle2 - angle1;

      // 处理角度跳跃
      if (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
      if (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;

      totalAngleChange += angleDiff;
    }

    // 检测是否完成了接近完整的圆
    if (Math.abs(totalAngleChange) > Math.PI * 1.5) {
      return totalAngleChange > 0 ? AdvancedGestureType.CIRCLE_COUNTER_CLOCKWISE : AdvancedGestureType.CIRCLE_CLOCKWISE;
    }

    return null;
  }

  /**
   * 获取主导轴
   */
  private getDominantAxis(vector: Vector3): 'x' | 'y' | 'z' {
    const absX = Math.abs(vector.x);
    const absY = Math.abs(vector.y);
    const absZ = Math.abs(vector.z);

    if (absX >= absY && absX >= absZ) return 'x';
    if (absY >= absZ) return 'y';
    return 'z';
  }

  // ===== 高级手势识别方法 =====

  /**
   * 识别OK手势
   */
  private isOKGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 拇指和食指形成圆圈，其他手指伸直
    return fingerCurvatures.thumb > 0.6 &&
           fingerCurvatures.index > 0.6 &&
           fingerCurvatures.middle < 0.3 &&
           fingerCurvatures.ring < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerDistances[0] < 0.05; // 拇指和食指距离很近
  }

  /**
   * 识别摇滚手势
   */
  private isRockGesture(fingerCurvatures: any): boolean {
    // 食指和小指伸直，其他手指弯曲
    return fingerCurvatures.index < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7;
  }

  /**
   * 识别打电话手势
   */
  private isCallMeGesture(fingerCurvatures: any): boolean {
    // 拇指和小指伸直，其他手指弯曲
    return fingerCurvatures.thumb < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerCurvatures.index > 0.7 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7;
  }

  /**
   * 识别手枪手势
   */
  private isFingerGunGesture(fingerCurvatures: any): boolean {
    // 食指和拇指伸直，其他手指弯曲
    return fingerCurvatures.index < 0.3 &&
           fingerCurvatures.thumb < 0.4 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7 &&
           fingerCurvatures.pinky > 0.7;
  }

  /**
   * 识别捏取手势
   */
  private isPinchGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 拇指和食指接近，其他手指相对伸直
    return fingerDistances[0] < 0.03 && // 拇指和食指很近
           fingerCurvatures.middle < 0.5 &&
           fingerCurvatures.ring < 0.5 &&
           fingerCurvatures.pinky < 0.5;
  }

  /**
   * 识别张开手指手势
   */
  private isSpreadFingersGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 所有手指伸直且分开
    const allStraight = Object.values(fingerCurvatures).every((curvature: any) => curvature < 0.3);
    const wellSpread = fingerDistances.every(distance => distance > 0.08);
    return allStraight && wellSpread;
  }

  /**
   * 识别拇指向下手势
   */
  private isThumbsDownGesture(fingerCurvatures: any): boolean {
    // 拇指向下，其他手指弯曲
    return fingerCurvatures.thumb < 0.3 &&
           fingerCurvatures.index > 0.7 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7 &&
           fingerCurvatures.pinky > 0.7;
  }

  /**
   * 计算伸直的手指数量
   */
  private countExtendedFingers(fingerCurvatures: any): number {
    let count = 0;
    const threshold = 0.4;

    if (fingerCurvatures.thumb < threshold) count++;
    if (fingerCurvatures.index < threshold) count++;
    if (fingerCurvatures.middle < threshold) count++;
    if (fingerCurvatures.ring < threshold) count++;
    if (fingerCurvatures.pinky < threshold) count++;

    return count;
  }

  /**
   * 获取数字手势类型
   */
  private getFingerCountGesture(count: number): AdvancedGestureType {
    switch (count) {
      case 1: return AdvancedGestureType.FINGER_COUNT_1;
      case 2: return AdvancedGestureType.FINGER_COUNT_2;
      case 3: return AdvancedGestureType.FINGER_COUNT_3;
      case 4: return AdvancedGestureType.FINGER_COUNT_4;
      case 5: return AdvancedGestureType.FINGER_COUNT_5;
      default: return AdvancedGestureType.OPEN_HAND;
    }
  }

  // ===== 基础手势识别方法（继承自原系统） =====

  private isGrabGesture(curvatures: any): boolean {
    const threshold = 0.7;
    return curvatures.index > threshold &&
           curvatures.middle > threshold &&
           curvatures.ring > threshold &&
           curvatures.pinky > threshold;
  }

  private isOpenHandGesture(curvatures: any): boolean {
    const threshold = 0.3;
    return curvatures.thumb < threshold &&
           curvatures.index < threshold &&
           curvatures.middle < threshold &&
           curvatures.ring < threshold &&
           curvatures.pinky < threshold;
  }

  private isPointingGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.index < straightThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  private isThumbsUpGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.thumb < straightThreshold &&
           curvatures.index > bentThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  // ===== 置信度计算方法 =====

  private calculateGrabConfidence(curvatures: any): number {
    const avgCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return Math.min(1, avgCurvature);
  }

  private calculateOpenHandConfidence(curvatures: any): number {
    const avgStraightness = 1 - (curvatures.thumb + curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 5;
    return Math.max(0, avgStraightness);
  }

  private calculatePointingConfidence(curvatures: any): number {
    const indexStraightness = 1 - curvatures.index;
    const othersCurvature = (curvatures.middle + curvatures.ring + curvatures.pinky) / 3;
    return (indexStraightness + othersCurvature) / 2;
  }

  private calculateThumbsUpConfidence(curvatures: any): number {
    const thumbStraightness = 1 - curvatures.thumb;
    const othersCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return (thumbStraightness + othersCurvature) / 2;
  }

  // ===== 辅助方法 =====

  /**
   * 创建手势结果
   */
  private createGestureResult(
    type: AdvancedGestureType,
    hand: 'left' | 'right',
    position: Vector3,
    features: GestureFeatures,
    confidence: number
  ): AdvancedGestureResult {
    return {
      type,
      confidence,
      hand,
      position,
      timestamp: Date.now(),
      duration: 0,
      velocity: new Vector3(),
      features
    };
  }

  /**
   * 创建动态手势结果
   */
  private createDynamicGestureResult(
    type: AdvancedGestureType,
    hand: string,
    position: Vector3,
    features: GestureFeatures,
    trajectory: Vector3[],
    velocity: Vector3,
    confidence: number
  ): AdvancedGestureResult {
    return {
      type,
      confidence,
      hand: hand as 'left' | 'right',
      position,
      timestamp: Date.now(),
      duration: 0,
      velocity,
      trajectory,
      features
    };
  }

  /**
   * 选择最佳手势
   */
  private selectBestGesture(
    staticGesture: AdvancedGestureResult | null,
    dynamicGesture: AdvancedGestureResult | null
  ): AdvancedGestureResult | null {
    if (!staticGesture && !dynamicGesture) return null;
    if (!staticGesture) return dynamicGesture;
    if (!dynamicGesture) return staticGesture;

    // 优先选择置信度更高的手势
    return staticGesture.confidence >= dynamicGesture.confidence ? staticGesture : dynamicGesture;
  }

  /**
   * 更新手势持续时间
   */
  private updateGestureDuration(handKey: string, gesture: AdvancedGestureResult, currentTime: number): void {
    const currentGesture = this.currentGestures.get(handKey);

    if (currentGesture && currentGesture.type === gesture.type) {
      // 同一手势，更新持续时间
      const startTime = this.gestureStartTimes.get(handKey) || currentTime;
      gesture.duration = currentTime - startTime;
    } else {
      // 新手势，重置开始时间
      this.gestureStartTimes.set(handKey, currentTime);
      gesture.duration = 0;
    }
  }

  /**
   * 检查手势序列
   */
  private checkGestureSequences(handKey: string, gesture: AdvancedGestureResult): void {
    if (!this.config.enableSequenceRecognition) return;

    const history = this.gestureHistory.get(handKey);
    if (!history) return;

    history.gestures.push(gesture.type);

    // 检查每个序列模式
    for (const pattern of this.sequencePatterns) {
      if (this.matchesSequencePattern(history.gestures, pattern)) {
        this.emit('sequenceDetected', {
          pattern: pattern.name,
          hand: handKey,
          confidence: pattern.minConfidence,
          timestamp: Date.now()
        });
      }
    }
  }

  /**
   * 匹配序列模式
   */
  private matchesSequencePattern(gestures: AdvancedGestureType[], pattern: GestureSequencePattern): boolean {
    if (gestures.length < pattern.pattern.length) return false;

    const recentGestures = gestures.slice(-pattern.pattern.length);
    return recentGestures.every((gesture, index) => gesture === pattern.pattern[index]);
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<AdvancedGestureConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): AdvancedGestureConfig {
    return { ...this.config };
  }

  /**
   * 重置识别器
   */
  public reset(): void {
    this.gestureHistory.clear();
    this.currentGestures.clear();
    this.gestureStartTimes.clear();
    this.emit('reset');
  }

  /**
   * 获取识别统计
   */
  public getRecognitionStats(): any {
    return {
      trackedHands: this.gestureHistory.size,
      currentGestures: Array.from(this.currentGestures.values()),
      sequencePatterns: this.sequencePatterns.length,
      averageHistorySize: Array.from(this.gestureHistory.values())
        .reduce((sum, history) => sum + history.positions.length, 0) / this.gestureHistory.size || 0
    };
  }

  /**
   * 添加自定义手势序列模式
   */
  public addSequencePattern(pattern: GestureSequencePattern): void {
    this.sequencePatterns.push(pattern);
    this.emit('sequencePatternAdded', pattern);
  }

  /**
   * 移除手势序列模式
   */
  public removeSequencePattern(patternName: string): boolean {
    const index = this.sequencePatterns.findIndex(p => p.name === patternName);
    if (index >= 0) {
      this.sequencePatterns.splice(index, 1);
      this.emit('sequencePatternRemoved', patternName);
      return true;
    }
    return false;
  }

  /**
   * 获取当前手势
   */
  public getCurrentGesture(hand: 'left' | 'right'): AdvancedGestureResult | null {
    return this.currentGestures.get(hand) || null;
  }

  /**
   * 获取手势历史
   */
  public getGestureHistory(hand: 'left' | 'right'): DynamicGestureHistory | null {
    return this.gestureHistory.get(hand) || null;
  }

  /**
   * 清除手势历史
   */
  public clearGestureHistory(hand?: 'left' | 'right'): void {
    if (hand) {
      this.gestureHistory.delete(hand);
      this.currentGestures.delete(hand);
      this.gestureStartTimes.delete(hand);
    } else {
      this.gestureHistory.clear();
      this.currentGestures.clear();
      this.gestureStartTimes.clear();
    }
    this.emit('historyCleared', hand);
  }
}

  // ===== 高级手势识别方法 =====

  /**
   * 识别OK手势
   */
  private isOKGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 拇指和食指形成圆圈，其他手指伸直
    return fingerCurvatures.thumb > 0.6 &&
           fingerCurvatures.index > 0.6 &&
           fingerCurvatures.middle < 0.3 &&
           fingerCurvatures.ring < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerDistances[0] < 0.05; // 拇指和食指距离很近
  }

  /**
   * 识别摇滚手势
   */
  private isRockGesture(fingerCurvatures: any): boolean {
    // 食指和小指伸直，其他手指弯曲
    return fingerCurvatures.index < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7;
  }

  /**
   * 识别打电话手势
   */
  private isCallMeGesture(fingerCurvatures: any): boolean {
    // 拇指和小指伸直，其他手指弯曲
    return fingerCurvatures.thumb < 0.3 &&
           fingerCurvatures.pinky < 0.3 &&
           fingerCurvatures.index > 0.7 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7;
  }

  /**
   * 识别手枪手势
   */
  private isFingerGunGesture(fingerCurvatures: any): boolean {
    // 食指和拇指伸直，其他手指弯曲
    return fingerCurvatures.index < 0.3 &&
           fingerCurvatures.thumb < 0.4 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7 &&
           fingerCurvatures.pinky > 0.7;
  }

  /**
   * 识别捏取手势
   */
  private isPinchGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 拇指和食指接近，其他手指相对伸直
    return fingerDistances[0] < 0.03 && // 拇指和食指很近
           fingerCurvatures.middle < 0.5 &&
           fingerCurvatures.ring < 0.5 &&
           fingerCurvatures.pinky < 0.5;
  }

  /**
   * 识别张开手指手势
   */
  private isSpreadFingersGesture(fingerCurvatures: any, fingerDistances: number[]): boolean {
    // 所有手指伸直且分开
    const allStraight = Object.values(fingerCurvatures).every((curvature: any) => curvature < 0.3);
    const wellSpread = fingerDistances.every(distance => distance > 0.08);
    return allStraight && wellSpread;
  }

  /**
   * 识别拇指向下手势
   */
  private isThumbsDownGesture(fingerCurvatures: any): boolean {
    // 拇指向下，其他手指弯曲
    return fingerCurvatures.thumb < 0.3 &&
           fingerCurvatures.index > 0.7 &&
           fingerCurvatures.middle > 0.7 &&
           fingerCurvatures.ring > 0.7 &&
           fingerCurvatures.pinky > 0.7;
  }

  /**
   * 计算伸直的手指数量
   */
  private countExtendedFingers(fingerCurvatures: any): number {
    let count = 0;
    const threshold = 0.4;

    if (fingerCurvatures.thumb < threshold) count++;
    if (fingerCurvatures.index < threshold) count++;
    if (fingerCurvatures.middle < threshold) count++;
    if (fingerCurvatures.ring < threshold) count++;
    if (fingerCurvatures.pinky < threshold) count++;

    return count;
  }

  /**
   * 获取数字手势类型
   */
  private getFingerCountGesture(count: number): AdvancedGestureType {
    switch (count) {
      case 1: return AdvancedGestureType.FINGER_COUNT_1;
      case 2: return AdvancedGestureType.FINGER_COUNT_2;
      case 3: return AdvancedGestureType.FINGER_COUNT_3;
      case 4: return AdvancedGestureType.FINGER_COUNT_4;
      case 5: return AdvancedGestureType.FINGER_COUNT_5;
      default: return AdvancedGestureType.OPEN_HAND;
    }
  }

  // ===== 基础手势识别方法（继承自原系统） =====

  private isGrabGesture(curvatures: any): boolean {
    const threshold = 0.7;
    return curvatures.index > threshold &&
           curvatures.middle > threshold &&
           curvatures.ring > threshold &&
           curvatures.pinky > threshold;
  }

  private isOpenHandGesture(curvatures: any): boolean {
    const threshold = 0.3;
    return curvatures.thumb < threshold &&
           curvatures.index < threshold &&
           curvatures.middle < threshold &&
           curvatures.ring < threshold &&
           curvatures.pinky < threshold;
  }

  private isPointingGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.index < straightThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  private isThumbsUpGesture(curvatures: any): boolean {
    const straightThreshold = 0.3;
    const bentThreshold = 0.7;
    return curvatures.thumb < straightThreshold &&
           curvatures.index > bentThreshold &&
           curvatures.middle > bentThreshold &&
           curvatures.ring > bentThreshold &&
           curvatures.pinky > bentThreshold;
  }

  // ===== 置信度计算方法 =====

  private calculateGrabConfidence(curvatures: any): number {
    const avgCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return Math.min(1, avgCurvature);
  }

  private calculateOpenHandConfidence(curvatures: any): number {
    const avgStraightness = 1 - (curvatures.thumb + curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 5;
    return Math.max(0, avgStraightness);
  }

  private calculatePointingConfidence(curvatures: any): number {
    const indexStraightness = 1 - curvatures.index;
    const othersCurvature = (curvatures.middle + curvatures.ring + curvatures.pinky) / 3;
    return (indexStraightness + othersCurvature) / 2;
  }

  private calculateThumbsUpConfidence(curvatures: any): number {
    const thumbStraightness = 1 - curvatures.thumb;
    const othersCurvature = (curvatures.index + curvatures.middle + curvatures.ring + curvatures.pinky) / 4;
    return (thumbStraightness + othersCurvature) / 2;
  }

  // ===== 辅助方法 =====

  /**
   * 创建手势结果
   */
  private createGestureResult(
    type: AdvancedGestureType,
    hand: 'left' | 'right',
    position: Vector3,
    features: GestureFeatures,
    confidence: number
  ): AdvancedGestureResult {
    return {
      type,
      confidence,
      hand,
      position,
      timestamp: Date.now(),
      duration: 0,
      velocity: new Vector3(),
      features
    };
  }

  /**
   * 创建动态手势结果
   */
  private createDynamicGestureResult(
    type: AdvancedGestureType,
    hand: string,
    position: Vector3,
    features: GestureFeatures,
    trajectory: Vector3[],
    velocity: Vector3,
    confidence: number
  ): AdvancedGestureResult {
    return {
      type,
      confidence,
      hand: hand as 'left' | 'right',
      position,
      timestamp: Date.now(),
      duration: 0,
      velocity,
      trajectory,
      features
    };
  }

  /**
   * 选择最佳手势
   */
  private selectBestGesture(
    staticGesture: AdvancedGestureResult | null,
    dynamicGesture: AdvancedGestureResult | null
  ): AdvancedGestureResult | null {
    if (!staticGesture && !dynamicGesture) return null;
    if (!staticGesture) return dynamicGesture;
    if (!dynamicGesture) return staticGesture;

    // 优先选择置信度更高的手势
    return staticGesture.confidence >= dynamicGesture.confidence ? staticGesture : dynamicGesture;
  }

  /**
   * 更新手势持续时间
   */
  private updateGestureDuration(handKey: string, gesture: AdvancedGestureResult, currentTime: number): void {
    const currentGesture = this.currentGestures.get(handKey);

    if (currentGesture && currentGesture.type === gesture.type) {
      // 同一手势，更新持续时间
      const startTime = this.gestureStartTimes.get(handKey) || currentTime;
      gesture.duration = currentTime - startTime;
    } else {
      // 新手势，重置开始时间
      this.gestureStartTimes.set(handKey, currentTime);
      gesture.duration = 0;
    }
  }

  /**
   * 检查手势序列
   */
  private checkGestureSequences(handKey: string, gesture: AdvancedGestureResult): void {
    if (!this.config.enableSequenceRecognition) return;

    const history = this.gestureHistory.get(handKey);
    if (!history) return;

    history.gestures.push(gesture.type);

    // 检查每个序列模式
    for (const pattern of this.sequencePatterns) {
      if (this.matchesSequencePattern(history.gestures, pattern)) {
        this.emit('sequenceDetected', {
          pattern: pattern.name,
          hand: handKey,
          confidence: pattern.minConfidence,
          timestamp: Date.now()
        });
      }
    }
  }

  /**
   * 匹配序列模式
   */
  private matchesSequencePattern(gestures: AdvancedGestureType[], pattern: GestureSequencePattern): boolean {
    if (gestures.length < pattern.pattern.length) return false;

    const recentGestures = gestures.slice(-pattern.pattern.length);
    return recentGestures.every((gesture, index) => gesture === pattern.pattern[index]);
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<AdvancedGestureConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * 获取配置
   */
  public getConfig(): AdvancedGestureConfig {
    return { ...this.config };
  }

  /**
   * 重置识别器
   */
  public reset(): void {
    this.gestureHistory.clear();
    this.currentGestures.clear();
    this.gestureStartTimes.clear();
    this.emit('reset');
  }

  /**
   * 获取识别统计
   */
  public getRecognitionStats(): any {
    return {
      trackedHands: this.gestureHistory.size,
      currentGestures: Array.from(this.currentGestures.values()),
      sequencePatterns: this.sequencePatterns.length,
      averageHistorySize: Array.from(this.gestureHistory.values())
        .reduce((sum, history) => sum + history.positions.length, 0) / this.gestureHistory.size || 0
    };
  }
｝