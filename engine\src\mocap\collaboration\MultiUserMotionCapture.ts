/**
 * 多用户动作捕捉系统
 * 支持大规模多用户同时动作捕捉和协作交互
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { EnhancedMotionCaptureSystem } from '../enhancement/EnhancedMotionCaptureSystem';
import { CollaborationManager } from './CollaborationManager';
import { SynchronizationManager } from './SynchronizationManager';
import { ConflictResolver } from './ConflictResolver';

/**
 * 用户状态
 */
export enum UserState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ACTIVE = 'active',
  IDLE = 'idle',
  ERROR = 'error'
}

/**
 * 用户信息
 */
export interface UserInfo {
  id: string;
  name: string;
  avatar?: string;
  role: UserRole;
  permissions: UserPermissions;
  state: UserState;
  lastActivity: number;
  entity?: Entity;
}

/**
 * 用户角色
 */
export enum UserRole {
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  PARTICIPANT = 'participant',
  OBSERVER = 'observer'
}

/**
 * 用户权限
 */
export interface UserPermissions {
  canInteract: boolean;
  canModifyObjects: boolean;
  canInviteUsers: boolean;
  canManageSession: boolean;
  maxInteractionDistance: number;
}

/**
 * 协作会话配置
 */
export interface CollaborationSessionConfig {
  /** 最大用户数 */
  maxUsers: number;
  /** 是否启用用户隔离 */
  enableUserSeparation: boolean;
  /** 是否启用协作交互 */
  enableCollaborativeInteraction: boolean;
  /** 是否启用冲突检测 */
  enableConflictDetection: boolean;
  /** 是否启用实时同步 */
  enableRealTimeSync: boolean;
  /** 同步频率 (Hz) */
  syncFrequency: number;
  /** 网络延迟补偿 */
  enableLatencyCompensation: boolean;
  /** 数据压缩 */
  enableDataCompression: boolean;
}

/**
 * 多用户动作捕捉系统
 */
export class MultiUserMotionCapture extends EventEmitter {
  private world: World;
  private config: CollaborationSessionConfig;
  private sessionId: string;
  
  // 核心组件
  private motionCaptureSystem: EnhancedMotionCaptureSystem;
  private collaborationManager: CollaborationManager;
  private synchronizationManager: SynchronizationManager;
  private conflictResolver: ConflictResolver;
  
  // 用户管理
  private users: Map<string, UserInfo> = new Map();
  private localUserId: string;
  private isHost = false;
  
  // 会话状态
  private isSessionActive = false;
  private sessionStartTime = 0;
  private networkManager: NetworkManager;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: CollaborationSessionConfig = {
    maxUsers: 8,
    enableUserSeparation: true,
    enableCollaborativeInteraction: true,
    enableConflictDetection: true,
    enableRealTimeSync: true,
    syncFrequency: 30,
    enableLatencyCompensation: true,
    enableDataCompression: true
  };

  constructor(
    world: World, 
    localUserId: string,
    config: Partial<CollaborationSessionConfig> = {}
  ) {
    super();
    this.world = world;
    this.localUserId = localUserId;
    this.config = { ...MultiUserMotionCapture.DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
    
    this.initializeComponents();
  }

  /**
   * 初始化组件
   */
  private initializeComponents(): void {
    // 初始化动作捕捉系统
    this.motionCaptureSystem = new EnhancedMotionCaptureSystem(this.world, {
      enablePerformanceOptimization: true,
      enableAutoTuning: true
    });

    // 初始化协作管理器
    this.collaborationManager = new CollaborationManager(this.config);
    
    // 初始化同步管理器
    this.synchronizationManager = new SynchronizationManager(this.config);
    
    // 初始化冲突解决器
    this.conflictResolver = new ConflictResolver(this.config);
    
    // 初始化网络管理器
    this.networkManager = new NetworkManager(this.config);

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 动作捕捉事件
    this.motionCaptureSystem.on('enhancedPoseDetected', (data) => {
      this.handleLocalPoseUpdate(data);
    });

    this.motionCaptureSystem.on('enhancedHandsDetected', (data) => {
      this.handleLocalHandsUpdate(data);
    });

    // 协作事件
    this.collaborationManager.on('collaborationDetected', (data) => {
      this.handleCollaborationEvent(data);
    });

    // 同步事件
    this.synchronizationManager.on('syncRequired', (data) => {
      this.handleSyncRequired(data);
    });

    // 冲突事件
    this.conflictResolver.on('conflictDetected', (data) => {
      this.handleConflictDetected(data);
    });

    // 网络事件
    this.networkManager.on('userJoined', (user) => {
      this.handleUserJoined(user);
    });

    this.networkManager.on('userLeft', (userId) => {
      this.handleUserLeft(userId);
    });

    this.networkManager.on('dataReceived', (data) => {
      this.handleNetworkData(data);
    });
  }

  /**
   * 创建协作会话
   */
  public async createSession(): Promise<string> {
    try {
      this.isHost = true;
      
      // 初始化动作捕捉系统
      await this.motionCaptureSystem.initialize();
      
      // 创建本地用户
      const localUser: UserInfo = {
        id: this.localUserId,
        name: `User_${this.localUserId}`,
        role: UserRole.ADMIN,
        permissions: {
          canInteract: true,
          canModifyObjects: true,
          canInviteUsers: true,
          canManageSession: true,
          maxInteractionDistance: 10.0
        },
        state: UserState.CONNECTED,
        lastActivity: Date.now()
      };
      
      this.users.set(this.localUserId, localUser);
      
      // 启动网络服务
      await this.networkManager.startHost(this.sessionId);
      
      this.isSessionActive = true;
      this.sessionStartTime = Date.now();
      
      Debug.log('MultiUserMotionCapture', `协作会话创建成功: ${this.sessionId}`);
      this.emit('sessionCreated', { sessionId: this.sessionId, isHost: true });
      
      return this.sessionId;

    } catch (error) {
      Debug.error('MultiUserMotionCapture', '创建会话失败', error);
      throw error;
    }
  }

  /**
   * 加入协作会话
   */
  public async joinSession(sessionId: string): Promise<boolean> {
    try {
      this.sessionId = sessionId;
      this.isHost = false;
      
      // 初始化动作捕捉系统
      await this.motionCaptureSystem.initialize();
      
      // 连接到会话
      const success = await this.networkManager.joinSession(sessionId);
      if (!success) {
        throw new Error('无法连接到会话');
      }
      
      // 创建本地用户
      const localUser: UserInfo = {
        id: this.localUserId,
        name: `User_${this.localUserId}`,
        role: UserRole.PARTICIPANT,
        permissions: {
          canInteract: true,
          canModifyObjects: true,
          canInviteUsers: false,
          canManageSession: false,
          maxInteractionDistance: 5.0
        },
        state: UserState.CONNECTED,
        lastActivity: Date.now()
      };
      
      this.users.set(this.localUserId, localUser);
      
      this.isSessionActive = true;
      
      Debug.log('MultiUserMotionCapture', `成功加入会话: ${sessionId}`);
      this.emit('sessionJoined', { sessionId, isHost: false });
      
      return true;

    } catch (error) {
      Debug.error('MultiUserMotionCapture', '加入会话失败', error);
      return false;
    }
  }

  /**
   * 处理本地姿态更新
   */
  private handleLocalPoseUpdate(data: any): void {
    if (!this.isSessionActive) return;

    const localUser = this.users.get(this.localUserId);
    if (!localUser || !localUser.entity) return;

    // 更新本地用户状态
    localUser.lastActivity = Date.now();
    localUser.state = UserState.ACTIVE;

    // 准备同步数据
    const syncData = {
      userId: this.localUserId,
      type: 'pose_update',
      data: {
        poseResults: data.poseResults,
        timestamp: Date.now()
      }
    };

    // 发送给其他用户
    if (this.config.enableRealTimeSync) {
      this.synchronizationManager.broadcastUpdate(syncData);
    }

    this.emit('localPoseUpdated', data);
  }

  /**
   * 处理本地手部更新
   */
  private handleLocalHandsUpdate(data: any): void {
    if (!this.isSessionActive) return;

    const localUser = this.users.get(this.localUserId);
    if (!localUser || !localUser.entity) return;

    // 检测协作交互
    if (this.config.enableCollaborativeInteraction) {
      this.collaborationManager.detectCollaboration(
        localUser.entity,
        data,
        Array.from(this.users.values()).filter(u => u.id !== this.localUserId)
      );
    }

    // 准备同步数据
    const syncData = {
      userId: this.localUserId,
      type: 'hands_update',
      data: {
        handResults: data.handResults,
        enhancedHandData: data.enhancedHandData,
        timestamp: Date.now()
      }
    };

    // 发送给其他用户
    if (this.config.enableRealTimeSync) {
      this.synchronizationManager.broadcastUpdate(syncData);
    }

    this.emit('localHandsUpdated', data);
  }

  /**
   * 处理用户加入
   */
  private handleUserJoined(user: UserInfo): void {
    this.users.set(user.id, user);
    
    // 为新用户创建实体
    const userEntity = this.createUserEntity(user);
    user.entity = userEntity;
    
    // 注册到动作捕捉系统
    this.motionCaptureSystem.registerEntity(userEntity);
    
    Debug.log('MultiUserMotionCapture', `用户加入: ${user.name} (${user.id})`);
    this.emit('userJoined', user);
  }

  /**
   * 处理用户离开
   */
  private handleUserLeft(userId: string): void {
    const user = this.users.get(userId);
    if (user) {
      // 注销实体
      if (user.entity) {
        this.motionCaptureSystem.unregisterEntity(user.entity);
        this.world.removeEntity(user.entity);
      }
      
      this.users.delete(userId);
      
      Debug.log('MultiUserMotionCapture', `用户离开: ${user.name} (${userId})`);
      this.emit('userLeft', user);
    }
  }

  /**
   * 处理网络数据
   */
  private handleNetworkData(data: any): void {
    switch (data.type) {
      case 'pose_update':
        this.handleRemotePoseUpdate(data);
        break;
      case 'hands_update':
        this.handleRemoteHandsUpdate(data);
        break;
      case 'interaction_event':
        this.handleRemoteInteractionEvent(data);
        break;
      case 'user_state_change':
        this.handleUserStateChange(data);
        break;
    }
  }

  /**
   * 处理远程姿态更新
   */
  private handleRemotePoseUpdate(data: any): void {
    const user = this.users.get(data.userId);
    if (!user || !user.entity) return;

    // 应用延迟补偿
    if (this.config.enableLatencyCompensation) {
      data.data = this.synchronizationManager.compensateLatency(data.data);
    }

    // 更新用户实体
    const motionCaptureComponent = user.entity.getComponent('MotionCaptureComponent');
    if (motionCaptureComponent) {
      (motionCaptureComponent as any).updatePoseData?.(data.data.poseResults);
    }

    user.lastActivity = Date.now();
    user.state = UserState.ACTIVE;

    this.emit('remotePoseUpdated', { user, data: data.data });
  }

  /**
   * 处理远程手部更新
   */
  private handleRemoteHandsUpdate(data: any): void {
    const user = this.users.get(data.userId);
    if (!user || !user.entity) return;

    // 检测冲突
    if (this.config.enableConflictDetection) {
      this.conflictResolver.checkInteractionConflicts(
        user.entity,
        data.data,
        Array.from(this.users.values())
      );
    }

    // 更新用户实体
    const motionCaptureComponent = user.entity.getComponent('MotionCaptureComponent');
    if (motionCaptureComponent) {
      (motionCaptureComponent as any).updateHandData?.(data.data.handResults);
    }

    user.lastActivity = Date.now();
    user.state = UserState.ACTIVE;

    this.emit('remoteHandsUpdated', { user, data: data.data });
  }

  /**
   * 处理远程交互事件
   */
  private handleRemoteInteractionEvent(data: any): void {
    const user = this.users.get(data.userId);
    if (!user) return;

    // 验证用户权限
    if (!this.validateUserPermissions(user, data.data.action)) {
      Debug.warn('MultiUserMotionCapture', `用户权限不足: ${user.id}`);
      return;
    }

    this.emit('remoteInteractionEvent', { user, data: data.data });
  }

  /**
   * 处理用户状态变化
   */
  private handleUserStateChange(data: any): void {
    const user = this.users.get(data.userId);
    if (user) {
      user.state = data.newState;
      this.emit('userStateChanged', { user, oldState: data.oldState, newState: data.newState });
    }
  }

  /**
   * 处理协作事件
   */
  private handleCollaborationEvent(data: any): void {
    Debug.log('MultiUserMotionCapture', '检测到协作交互', data);
    this.emit('collaborationDetected', data);
  }

  /**
   * 处理同步需求
   */
  private handleSyncRequired(data: any): void {
    // 执行同步操作
    this.performSynchronization(data);
  }

  /**
   * 处理冲突检测
   */
  private handleConflictDetected(data: any): void {
    Debug.warn('MultiUserMotionCapture', '检测到交互冲突', data);
    
    // 解决冲突
    const resolution = this.conflictResolver.resolveConflict(data);
    
    this.emit('conflictDetected', { conflict: data, resolution });
  }

  /**
   * 创建用户实体
   */
  private createUserEntity(user: UserInfo): Entity {
    const entity = this.world.createEntity(`user_${user.id}`);
    
    // 添加必要的组件
    entity.addComponent('Transform', {
      position: { x: 0, y: 0, z: 0 },
      rotation: { x: 0, y: 0, z: 0, w: 1 },
      scale: { x: 1, y: 1, z: 1 }
    });
    
    entity.addComponent('MotionCaptureComponent', {
      userId: user.id,
      isRemote: user.id !== this.localUserId
    });
    
    entity.addComponent('CollaborationComponent', {
      userId: user.id,
      role: user.role,
      permissions: user.permissions
    });

    return entity;
  }

  /**
   * 验证用户权限
   */
  private validateUserPermissions(user: UserInfo, action: string): boolean {
    switch (action) {
      case 'modify_object':
        return user.permissions.canModifyObjects;
      case 'interact':
        return user.permissions.canInteract;
      case 'invite_user':
        return user.permissions.canInviteUsers;
      case 'manage_session':
        return user.permissions.canManageSession;
      default:
        return true;
    }
  }

  /**
   * 执行同步
   */
  private performSynchronization(data: any): void {
    // 实现同步逻辑
    this.synchronizationManager.performSync(data);
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取用户列表
   */
  public getUsers(): UserInfo[] {
    return Array.from(this.users.values());
  }

  /**
   * 获取用户信息
   */
  public getUser(userId: string): UserInfo | null {
    return this.users.get(userId) || null;
  }

  /**
   * 更新用户权限
   */
  public updateUserPermissions(userId: string, permissions: Partial<UserPermissions>): boolean {
    const user = this.users.get(userId);
    if (!user) return false;

    // 只有管理员可以修改权限
    const localUser = this.users.get(this.localUserId);
    if (!localUser || localUser.role !== UserRole.ADMIN) {
      return false;
    }

    user.permissions = { ...user.permissions, ...permissions };
    
    // 广播权限变化
    this.networkManager.broadcast({
      type: 'permission_update',
      userId,
      permissions: user.permissions
    });

    this.emit('userPermissionsUpdated', { user, permissions });
    return true;
  }

  /**
   * 踢出用户
   */
  public kickUser(userId: string): boolean {
    if (!this.isHost) return false;

    const user = this.users.get(userId);
    if (!user) return false;

    // 发送踢出消息
    this.networkManager.sendToUser(userId, {
      type: 'kicked',
      reason: 'Kicked by host'
    });

    // 移除用户
    this.handleUserLeft(userId);
    
    return true;
  }

  /**
   * 离开会话
   */
  public async leaveSession(): Promise<void> {
    if (!this.isSessionActive) return;

    try {
      this.isSessionActive = false;
      
      // 通知其他用户
      this.networkManager.broadcast({
        type: 'user_leaving',
        userId: this.localUserId
      });
      
      // 停止网络连接
      await this.networkManager.disconnect();
      
      // 停止动作捕捉
      await this.motionCaptureSystem.disable();
      
      // 清理用户
      this.users.clear();
      
      Debug.log('MultiUserMotionCapture', '已离开会话');
      this.emit('sessionLeft');

    } catch (error) {
      Debug.error('MultiUserMotionCapture', '离开会话失败', error);
    }
  }

  /**
   * 销毁系统
   */
  public async destroy(): Promise<void> {
    await this.leaveSession();
    await this.motionCaptureSystem.destroy();
    
    this.collaborationManager.destroy();
    this.synchronizationManager.destroy();
    this.conflictResolver.destroy();
    this.networkManager.destroy();
    
    this.removeAllListeners();
  }

  /**
   * 获取会话统计
   */
  public getSessionStats(): any {
    return {
      sessionId: this.sessionId,
      isHost: this.isHost,
      isActive: this.isSessionActive,
      userCount: this.users.size,
      sessionDuration: this.isSessionActive ? Date.now() - this.sessionStartTime : 0,
      networkStats: this.networkManager.getStats(),
      syncStats: this.synchronizationManager.getStats()
    };
  }
}

/**
 * 网络管理器（简化实现）
 */
class NetworkManager extends EventEmitter {
  private config: CollaborationSessionConfig;
  private isHost = false;
  private connections: Map<string, any> = new Map();

  constructor(config: CollaborationSessionConfig) {
    super();
    this.config = config;
  }

  public async startHost(sessionId: string): Promise<void> {
    this.isHost = true;
    // 实现主机网络逻辑
  }

  public async joinSession(sessionId: string): Promise<boolean> {
    // 实现加入会话逻辑
    return true;
  }

  public broadcast(data: any): void {
    // 实现广播逻辑
  }

  public sendToUser(userId: string, data: any): void {
    // 实现点对点发送逻辑
  }

  public async disconnect(): Promise<void> {
    // 实现断开连接逻辑
  }

  public getStats(): any {
    return {
      connectionCount: this.connections.size,
      isHost: this.isHost
    };
  }

  public destroy(): void {
    this.connections.clear();
    this.removeAllListeners();
  }
}
